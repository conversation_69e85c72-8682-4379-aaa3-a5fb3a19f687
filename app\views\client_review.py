#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客户审核视图
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from app.models.content import Content, RejectionReason
from app.models.image import ContentImage
from app.models.client import Client
from app.utils.share_link import ShareLinkGenerator
from app import db, csrf
from datetime import datetime

# 创建蓝图
client_review_bp = Blueprint('client_review', __name__, url_prefix='/client-review')

@client_review_bp.route('/<share_key>')
def review_page(share_key):
    """客户审核页面"""
    # 获取访问密钥（如果有）
    access_key = request.args.get('key')

    # 验证分享密钥
    is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)

    if not is_valid:
        # 如果是需要访问密钥的错误，显示密钥输入页面
        if error_msg == "需要访问密钥":
            return render_template('client_review/access_key.html',
                                 share_key=share_key)
        return render_template('client_review/error.html',
                             error_message=error_msg,
                             error_code='INVALID_LINK'), 403

    # 获取客户信息
    client = Client.query.get(client_id)
    if not client:
        return render_template('client_review/error.html',
                             error_message='客户不存在',
                             error_code='CLIENT_NOT_FOUND'), 404

    # 获取分享链接统计
    stats = ShareLinkGenerator.get_share_link_stats(share_key)

    # 使用响应式模板，自动适应所有设备
    return render_template('client_review/responsive.html',
                         client=client,
                         share_key=share_key,
                         stats=stats)



@client_review_bp.route('/<share_key>/content/<int:content_id>')
def content_detail(share_key, content_id):
    """文章详情页面"""
    # 获取访问密钥（如果有）
    access_key = request.args.get('key')

    # 验证分享密钥
    is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)

    if not is_valid:
        # 如果是需要访问密钥的错误，显示密钥输入页面
        if error_msg == "需要访问密钥":
            return render_template('client_review/access_key.html',
                                 share_key=share_key)
        return render_template('client_review/error.html',
                             error_message=error_msg,
                             error_code='INVALID_LINK'), 403

    # 获取客户信息
    client = Client.query.get(client_id)
    if not client:
        return render_template('client_review/error.html',
                             error_message='客户不存在',
                             error_code='CLIENT_NOT_FOUND'), 404

    # 获取文章内容
    content = Content.query.filter_by(id=content_id, client_id=client_id).first()
    if not content:
        return render_template('client_review/error.html',
                             error_message='文章不存在或无权限访问',
                             error_code='CONTENT_NOT_FOUND'), 404

    # 检查文章状态 - 只有终审通过或已通过的文章才能访问，驳回的文章不能访问
    if not (content.workflow_status == 'pending_client_review' or
            content.client_review_status in ['approved', 'auto_approved']):
        return render_template('client_review/error.html',
                             error_message='文章当前状态不允许访问',
                             error_code='ACCESS_DENIED'), 403

    # 使用响应式模板，自动适应所有设备
    return render_template('client_review/content_responsive.html',
                         client=client,
                         content=content,
                         share_key=share_key)




@client_review_bp.route('/api/<share_key>/contents')
def get_contents(share_key):
    """获取客户的文案列表API"""
    try:
        # 获取访问密钥（如果有）
        access_key = request.args.get('key')

        # 验证分享密钥
        is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)

        if not is_valid:
            return jsonify({
                'success': False,
                'message': error_msg
            }), 403
        
        # 获取筛选参数
        task_name = request.args.get('task_name', '').strip()
        batch_name = request.args.get('batch_name', '').strip()
        status = request.args.get('status', '').strip()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        # 构建查询
        query = Content.query.filter_by(client_id=client_id, is_deleted=False)
        
        # 根据状态筛选文案
        if status == 'pending':
            # 待审核：必须是终审通过且客户审核状态为pending
            query = query.filter(
                Content.workflow_status == 'pending_client_review',
                Content.client_review_status == 'pending'
            )
        elif status == 'approved':
            # 已通过但未发布：客户审核状态为approved或auto_approved且未发布
            query = query.filter(
                Content.client_review_status.in_(['approved', 'auto_approved']),
                Content.publish_status != 'published'
            )
        elif status == 'published':
            # 已发布：发布状态为published
            query = query.filter(Content.publish_status == 'published')
        elif status == 'rejected':
            # 驳回的文章不显示给客户，返回空结果
            query = query.filter(Content.id == -1)  # 永远不会匹配的条件
        else:
            # 如果没有指定状态，只显示终审通过的文案（不包括驳回的）
            query = query.filter(
                (Content.workflow_status == 'pending_client_review') |
                (Content.client_review_status.in_(['approved', 'auto_approved']))
            )
        
        # 任务筛选
        if task_name:
            from app.models.task import Task
            task_ids = db.session.query(Task.id).filter(
                Task.name.like(f'%{task_name}%')
            ).subquery()
            query = query.filter(Content.task_id.in_(task_ids))
        
        # 批次筛选
        if batch_name:
            from app.models.task import Batch
            batch_ids = db.session.query(Batch.id).filter(
                Batch.name.like(f'%{batch_name}%')
            ).subquery()
            query = query.filter(Content.batch_id.in_(batch_ids))
        
        # 分页
        pagination = query.order_by(Content.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # 为每个文案获取图片信息和驳回理由
        content_data = []
        for content in pagination.items:
            images = ContentImage.get_by_content(content.id)

            # 获取最新的驳回理由
            latest_rejection = RejectionReason.query.filter_by(content_id=content.id)\
                                                  .order_by(RejectionReason.created_at.desc())\
                                                  .first()

            content_data.append({
                'id': content.id,
                'title': content.title,
                'content': content.content,
                'topics': content.topics,
                'location': content.location,
                'workflow_status': content.workflow_status,
                'client_review_status': content.client_review_status,
                'publish_status': content.publish_status or 'unpublished',
                'created_at': content.created_at.isoformat() if content.created_at else None,
                'display_date': content.display_date.isoformat() if content.display_date else None,
                'display_time': content.display_time.strftime('%H:%M') if content.display_time else None,
                'task_name': content.task.name if content.task else None,
                'batch_name': content.batch.name if content.batch else None,
                'images': [img.to_dict() for img in images],
                'image_count': len(images),
                'latest_rejection': {
                    'reason': latest_rejection.reason,
                    'is_client': latest_rejection.is_client,
                    'created_at': latest_rejection.created_at.isoformat(),
                    'creator_name': latest_rejection.creator.username if latest_rejection.creator else '客户'
                } if latest_rejection else None
            })
        
        return jsonify({
            'success': True,
            'contents': content_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取文案列表失败：{str(e)}'
        }), 500


@client_review_bp.route('/api/<share_key>/contents/<int:content_id>/review', methods=['POST'])
@csrf.exempt
def review_content(share_key, content_id):
    """客户审核文案API"""
    try:
        # 获取访问密钥（如果有）
        access_key = request.args.get('key') or request.form.get('key')

        # 验证分享密钥
        is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)

        if not is_valid:
            return jsonify({
                'success': False,
                'message': error_msg
            }), 403
        
        # 获取文案
        content = Content.query.filter_by(id=content_id, client_id=client_id).first()
        if not content:
            return jsonify({
                'success': False,
                'message': '文案不存在或无权限访问'
            }), 404
        
        # 检查文案状态 - 主要看工作流状态是否为待客户审核
        if content.workflow_status != 'pending_client_review':
            return jsonify({
                'success': False,
                'message': '文案当前状态不允许客户审核'
            }), 400
        
        # 获取审核参数
        action = request.form.get('action')  # 'approve' 或 'reject'
        review_comment = request.form.get('review_comment', '').strip()  # 前端传递的是review_comment参数
        
        if action == 'approve':
            # 客户审核通过
            content.client_review_status = 'approved'
            content.client_review_time = datetime.now()

            # 检查是否需要手动发布（统一逻辑：关闭 = 自动化）
            from app.models.system_setting import SystemSetting
            auto_publish_enabled = SystemSetting.get_value('auto_publish_enabled', 'false')

            if auto_publish_enabled.lower() in ['false', '0']:
                # 关闭手动发布开关，自动发布，直接进入待发布状态
                content.workflow_status = 'pending_publish'
                content.publish_status = 'pending_publish'
                content.status_update_time = datetime.now()
                message = '审核通过，已自动提交发布'
            else:
                # 开启手动发布开关，进入准备发布状态，需要手动提交
                content.workflow_status = 'ready_to_publish'
                message = '审核通过'
            
        elif action == 'reject':
            # 获取驳回类型
            rejection_type = request.form.get('rejection_type', 'content')

            # 客户审核驳回
            content.client_review_status = 'rejected'
            content.client_review_time = datetime.now()

            # 根据驳回类型决定流转状态（使用类似最终审核驳回的逻辑）
            if rejection_type == 'image':
                # 只有图片问题，回到图片上传阶段
                content.workflow_status = 'first_reviewed'
                content.internal_review_status = 'client_rej_img'  # 标记为客户驳回（图片问题）
                content.content_completed = 1  # 文案没问题
                content.image_completed = 0    # 图片需要重新上传
                message = '客户审核驳回（图片问题），请重新上传图片'
            elif rejection_type == 'both':
                # 文案和图片都有问题，回到草稿状态
                content.workflow_status = 'draft'
                content.internal_review_status = 'client_rej_both'  # 标记为客户驳回（两者都有问题）
                content.content_completed = 0  # 文案需要重新编辑
                content.image_completed = 0    # 图片需要重新上传
                message = '客户审核驳回（文案+图片问题），请重新编写文案和上传图片'
            else:
                # 文案问题，回到草稿状态
                content.workflow_status = 'draft'
                content.internal_review_status = 'client_rej_text'  # 标记为客户驳回（文案问题）
                content.content_completed = 0  # 文案需要重新编辑
                content.image_completed = 1    # 图片没问题
                message = '客户审核驳回（文案问题），请重新编写文案'

            # 记录驳回理由
            if review_comment:
                from app.models.content import RejectionReason
                rejection = RejectionReason(
                    content_id=content.id,
                    reason=review_comment,
                    rejection_type=rejection_type,
                    created_by=None,  # 客户操作，没有用户ID
                    is_client=True,  # 标记为客户创建
                    created_at=datetime.now()
                )
                db.session.add(rejection)
            
        else:
            return jsonify({
                'success': False,
                'message': '无效的审核动作'
            }), 400
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': message,
            'new_status': content.workflow_status
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'审核失败：{str(e)}'
        }), 500


@client_review_bp.route('/api/<share_key>/contents/<int:content_id>')
@csrf.exempt
def get_content_detail(share_key, content_id):
    """获取文案详情API"""
    try:
        # 获取访问密钥（如果有）
        access_key = request.args.get('key')

        # 验证分享密钥
        is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)

        if not is_valid:
            return jsonify({
                'success': False,
                'message': error_msg
            }), 403

        # 获取文案
        content = Content.query.filter_by(id=content_id, client_id=client_id).first()
        if not content:
            return jsonify({
                'success': False,
                'message': '文案不存在或无权限访问'
            }), 404

        # 解析话题
        topics_text = ''
        if content.topics:
            try:
                import json
                topics = json.loads(content.topics)
                topics_text = ','.join(topics) if isinstance(topics, list) else str(topics)
            except:
                topics_text = content.topics or ''

        # 解析@用户
        mentions_text = ''
        at_users = content.at_users_list
        if at_users:
            mentions_text = ','.join(at_users)

        return jsonify({
            'success': True,
            'content': {
                'id': content.id,
                'title': content.title,
                'content': content.content,
                'topics_text': topics_text,
                'location': content.location or '',
                'mentions': mentions_text,
                'workflow_status': content.workflow_status,
                'client_review_status': content.client_review_status,
                'client_name': content.client.name if content.client else '',
                'created_at': content.created_at.isoformat() if content.created_at else ''
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取文案详情失败：{str(e)}'
        }), 500


@client_review_bp.route('/api/<share_key>/contents/<int:content_id>/images')
@csrf.exempt
def get_content_images(share_key, content_id):
    """获取文案图片API"""
    try:
        # 获取访问密钥（如果有）
        access_key = request.args.get('key')

        # 验证分享密钥
        is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)

        if not is_valid:
            return jsonify({
                'success': False,
                'message': error_msg
            }), 403

        # 获取文案
        content = Content.query.filter_by(id=content_id, client_id=client_id).first()
        if not content:
            return jsonify({
                'success': False,
                'message': '文案不存在或无权限访问'
            }), 404

        # 获取图片
        images = ContentImage.get_by_content(content_id)
        image_data = []

        for image in images:
            image_data.append({
                'id': image.id,
                'url': image.url,
                'filename': image.filename,
                'upload_time': image.upload_time.isoformat() if image.upload_time else ''
            })

        return jsonify({
            'success': True,
            'images': image_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取图片失败：{str(e)}'
        }), 500


@client_review_bp.route('/api/<share_key>/contents/<int:content_id>/edit', methods=['POST'])
@csrf.exempt
def edit_content(share_key, content_id):
    """客户编辑文案API"""
    try:
        # 获取访问密钥（如果有）
        access_key = request.args.get('key') or request.form.get('key')

        # 验证分享密钥
        is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)
        
        if not is_valid:
            return jsonify({
                'success': False,
                'message': error_msg
            }), 403
        
        # 获取文案
        content = Content.query.filter_by(id=content_id, client_id=client_id).first()
        if not content:
            return jsonify({
                'success': False,
                'message': '文案不存在或无权限访问'
            }), 404
        
        # 检查文案状态
        if content.workflow_status != 'pending_client_review':
            return jsonify({
                'success': False,
                'message': '文案状态不正确，无法编辑'
            }), 400
        
        # 获取编辑参数
        title = request.form.get('title', '').strip()
        content_text = request.form.get('content', '').strip()
        topics = request.form.get('topics', '').strip()
        mentions = request.form.get('mentions', '').strip()
        location = request.form.get('location', '').strip()
        
        if not title or not content_text:
            return jsonify({
                'success': False,
                'message': '标题和内容不能为空'
            }), 400
        
        # 字符限制验证
        def calculate_title_length(text):
            """计算标题长度：字母数字算0.5个，emoji算1个"""
            length = 0
            for char in text:
                if ord(char) > 127:
                    # 检查是否是中文字符
                    if '\u4e00' <= char <= '\u9fff':
                        length += 1  # 中文字符算1个
                    else:
                        length += 1  # emoji算1个
                else:
                    length += 0.5  # 字母数字算0.5个
            return int(length + 0.5)  # 四舍五入

        def calculate_content_length(text):
            """计算内容长度：中文算2个，其他算1个"""
            length = 0
            for char in text:
                if '\u4e00' <= char <= '\u9fff':
                    length += 2  # 中文字符算2个
                else:
                    length += 1  # 其他字符算1个
            return length

        # 验证标题长度
        title_length = calculate_title_length(title)
        if title_length > 20:
            return jsonify({
                'success': False,
                'message': f'标题超出限制！当前{title_length}个字符，最多20个字符'
            }), 400

        # 验证内容长度
        content_length = calculate_content_length(content_text)
        if content_length > 1000:
            return jsonify({
                'success': False,
                'message': f'内容超出限制！当前{content_length}个字符，最多1000个字符'
            }), 400
        
        # 处理话题 - 将逗号分隔的话题转换为JSON数组
        topics_json = None
        if topics:
            try:
                import json
                # 将逗号分隔的话题转换为JSON数组
                topics_list = [topic.strip() for topic in topics.split(',') if topic.strip()]
                topics_json = json.dumps(topics_list, ensure_ascii=False)
            except:
                topics_json = topics  # 如果转换失败，保持原样

        # 处理@用户 - 将逗号分隔的用户转换为列表
        mentions_list = []
        if mentions:
            mentions_list = [mention.strip() for mention in mentions.split(',') if mention.strip()]

        # 更新文案
        content.title = title
        content.content = content_text
        content.topics = topics_json
        content.at_users_list = mentions_list  # 使用模型的属性
        content.location = location
        content.updated_at = datetime.now()
        
        # 记录编辑历史
        from app.models.content import ContentHistory
        history = ContentHistory(
            content_id=content.id,
            action='client_edited',
            comment=f'客户编辑了文案内容',
            user_id=None,  # 客户操作，没有用户ID
            created_at=datetime.now()
        )
        db.session.add(history)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文案编辑成功'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'编辑失败：{str(e)}'
        }), 500


@client_review_bp.route('/api/<share_key>/stats')
def get_stats(share_key):
    """获取客户审核统计信息API"""
    try:
        # 获取访问密钥（如果有）
        access_key = request.args.get('key')

        # 验证分享密钥
        is_valid, client_id, error_msg = ShareLinkGenerator.validate_share_key(share_key, access_key)

        if not is_valid:
            return jsonify({
                'success': False,
                'message': error_msg
            }), 403
        
        # 获取统计信息
        stats = ShareLinkGenerator.get_share_link_stats(share_key)
        
        if stats:
            return jsonify({
                'success': True,
                'stats': {
                    'pending_count': stats['pending_count'],
                    'approved_count': stats['approved_count'],
                    'rejected_count': stats['rejected_count'],
                    'reviewed_count': stats['reviewed_count'],
                    'published_count': stats['published_count'],
                    'total_count': stats['total_count'],
                    'is_expired': stats['is_expired'],
                    'days_remaining': stats['days_remaining']
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '获取统计信息失败'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计信息失败：{str(e)}'
        }), 500
