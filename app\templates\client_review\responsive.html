<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{{ client.name }} - 文案审核 v4</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* 响应式头部 */
        .responsive-header {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(255, 36, 66, 0.3);
        }
        
        /* 桌面版头部 */
        @media (min-width: 768px) {
            .responsive-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 2rem 0;
                position: relative;
                margin-bottom: 2rem;
            }
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .header-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        
        @media (min-width: 768px) {
            .header-title {
                font-size: 2rem;
                text-align: left;
            }
        }
        
        .header-subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
            margin-top: 0.25rem;
        }
        
        @media (min-width: 768px) {
            .header-subtitle {
                font-size: 1rem;
                margin-top: 0.5rem;
            }
        }
        
        /* 统计卡片 */
        .stats-container {
            padding: 1rem;
            background: white;
            margin-bottom: 0.5rem;
        }
        
        @media (min-width: 768px) {
            .stats-container {
                margin: 0 auto 2rem;
                max-width: 1200px;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            text-align: center;
        }

        @media (min-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        .stat-item {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        @media (min-width: 768px) {
            .stat-item {
                padding: 1.5rem;
                border-radius: 15px;
                transition: transform 0.2s;
            }
            
            .stat-item:hover {
                transform: translateY(-2px);
            }
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff2442;
            display: block;
        }
        
        @media (min-width: 768px) {
            .stat-number {
                font-size: 2rem;
                color: #667eea;
            }
        }
        
        .stat-label {
            font-size: 0.75rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        @media (min-width: 768px) {
            .stat-label {
                font-size: 0.9rem;
                margin-top: 0.5rem;
            }
        }
        
        /* 筛选栏 */
        .filter-bar {
            background: white;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        @media (min-width: 768px) {
            .filter-bar {
                margin: 0 auto 2rem;
                max-width: 1200px;
                border-radius: 15px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-bottom: none;
            }
        }
        
        .filter-tabs {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }
        
        @media (min-width: 768px) {
            .filter-tabs {
                justify-content: center;
                overflow-x: visible;
                padding-bottom: 0;
            }
        }
        
        .filter-tab {
            background: #f8f9fa;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            white-space: nowrap;
            color: #666;
            transition: all 0.2s;
        }
        
        @media (min-width: 768px) {
            .filter-tab {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
            }
        }
        
        .filter-tab.active {
            background: #ff2442;
            color: white;
        }
        
        @media (min-width: 768px) {
            .filter-tab.active {
                background: #667eea;
            }
        }
        
        /* 内容列表 */
        .content-list {
            padding: 0 1rem;
        }
        
        @media (min-width: 768px) {
            .content-list {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0;
            }
        }
        
        /* 内容卡片 */
        .content-card {
            background: white;
            border-radius: 12px;
            margin: 0.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.2s ease;
        }
        
        @media (min-width: 768px) {
            .content-card {
                margin: 0 auto 1rem;
                max-width: 1200px;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
        }
        
        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        /* 图片样式 */
        .content-images {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .content-image {
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .content-image:hover {
            transform: scale(1.05);
        }
        
        .content-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        @media (min-width: 768px) {
            .content-images {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 0.75rem;
            }
        }
        
        /* 内容元信息 */
        .content-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: #999;
            margin-bottom: 1rem;
        }
        
        @media (min-width: 768px) {
            .content-meta {
                font-size: 0.85rem;
            }
        }
        
        /* 移动端内容布局 */
        .content-header {
            padding: 1rem 1rem 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .content-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin: 0;
            flex: 1;
            margin-right: 0.5rem;
        }
        
        /* 桌面端内容布局 */
        @media (min-width: 768px) {
            .content-header {
                padding: 1.5rem 1.5rem 1rem;
            }
            
            .content-title {
                font-size: 1.2rem;
                margin-right: 1rem;
            }
        }
        
        .content-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d1edff;
            color: #0c5460;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .content-body {
            padding: 0 1rem;
        }
        
        @media (min-width: 768px) {
            .content-body {
                padding: 0 1.5rem;
            }
        }
        
        .content-text {
            font-size: 0.9rem;
            line-height: 1.5;
            color: #333;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        @media (min-width: 768px) {
            .content-text {
                font-size: 1rem;
                line-height: 1.6;
                -webkit-line-clamp: 3;
            }
        }
        
        /* 操作按钮 */
        .content-actions {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            border-top: 1px solid #f0f0f0;
        }
        
        @media (min-width: 768px) {
            .content-actions {
                padding: 1.5rem;
                gap: 0.75rem;
            }
        }
        
        .action-btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        @media (min-width: 768px) {
            .action-btn {
                flex: none;
                min-width: 120px;
                padding: 0.875rem 1.5rem;
                font-size: 1rem;
            }
        }
        
        .btn-view {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-view:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .btn-approve {
            background: #ff2442;
            color: white;
        }
        
        .btn-approve:hover {
            background: #e01e3a;
            color: white;
        }
        
        @media (min-width: 768px) {
            .btn-approve {
                background: #28a745;
            }
            
            .btn-approve:hover {
                background: #218838;
            }
        }
        
        .btn-reject {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-reject:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        @media (min-width: 768px) {
            .btn-reject {
                background: #dc3545;
                color: white;
                border: none;
            }

            .btn-reject:hover {
                background: #c82333;
            }
        }

        .btn-edit {
            background: #ffc107;
            color: #212529;
            border: 1px solid #ffc107;
        }

        .btn-edit:hover {
            background: #ffca2c;
            color: #212529;
        }

        @media (min-width: 768px) {
            .btn-edit {
                background: #17a2b8;
                color: white;
                border: none;
            }

            .btn-edit:hover {
                background: #138496;
            }
        }
        
        /* 加载更多 */
        .load-more {
            text-align: center;
            padding: 2rem 1rem;
        }
        
        .load-more-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #666;
            padding: 0.75rem 2rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #999;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom);
            background: #f5f5f5;
        }
        
        @media (min-width: 768px) {
            .safe-area-bottom {
                display: none;
            }
        }
        
        /* 驳回类型卡片样式 */
        .rejection-type-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
        }

        .rejection-type-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        }

        .rejection-type-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .rejection-type-card .form-check-input {
            transform: scale(1.2);
        }

        .quick-reason-btn {
            transition: all 0.2s ease;
        }

        .quick-reason-btn:hover {
            transform: translateY(-1px);
        }

        .quick-reason-btn.selected {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <!-- 响应式头部 -->
    <div class="responsive-header">
        <div class="container-fluid">
            <div class="header-content">
                <div>
                    <h1 class="header-title">{{ client.name }}</h1>
                    <div class="header-subtitle">文案审核</div>
                    <!-- 调试信息 -->
                    <div class="d-none" id="debug-info">
                        ShareKey: {{ share_key }}
                        Client: {{ client.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-container">
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number" id="totalCount">-</span>
                <div class="stat-label">总文案</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="pendingCount">-</span>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="reviewedCount">-</span>
                <div class="stat-label">已审核</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="publishedCount">-</span>
                <div class="stat-label">已发布</div>
            </div>
        </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
        <div class="filter-tabs">
            <button class="filter-tab active" data-status="">全部</button>
            <button class="filter-tab" data-status="pending">待审核</button>
            <button class="filter-tab" data-status="approved">待发布</button>
            <button class="filter-tab" data-status="published">已发布</button>
        </div>
    </div>

    <!-- 内容列表 -->
    <div class="content-list" id="contentList">
        <!-- 内容将通过JavaScript动态加载 -->
    </div>

    <!-- 加载更多 -->
    <div class="load-more" id="loadMore" style="display: none;">
        <button class="load-more-btn" onclick="loadMoreContent()">
            <i class="bi bi-arrow-clockwise me-2"></i>加载更多
        </button>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" id="emptyState" style="display: none;">
        <i class="bi bi-inbox"></i>
        <div>暂无文案内容</div>
    </div>

    <!-- 编辑文案模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-fullscreen-lg-down modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil"></i> 编辑文案
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 左侧编辑区域 -->
                        <div class="col-lg-8">
                            <form id="editForm">
                                <!-- 标题 -->
                                <div class="mb-3">
                                    <label for="editTitle" class="form-label fw-bold">
                                        <i class="bi bi-type"></i> 标题
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-check-circle text-success"></i>
                                        </span>
                                        <input type="text" class="form-control" id="editTitle" maxlength="20" required>
                                    </div>
                                    <div class="form-text">
                                        字符统计: <span id="titleCount">0</span>/20
                                        <span class="text-danger" id="titleWarning" style="display: none;">超出限制!</span>
                                    </div>
                                </div>

                                <!-- 文案内容 -->
                                <div class="mb-3">
                                    <label for="editContent" class="form-label fw-bold">文案内容</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-check-circle text-success"></i>
                                        </span>
                                        <textarea class="form-control" id="editContent" rows="8" maxlength="1000" required></textarea>
                                    </div>
                                    <div class="form-text">
                                        字符统计: <span id="contentCount">0</span>/1000
                                        <span class="text-success" id="contentRequirement">符合要求</span>
                                    </div>
                                </div>

                                <!-- 配图预览 -->
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="bi bi-images"></i> 配图预览
                                    </label>
                                    <div class="border rounded p-3">
                                        <!-- 上传按钮 -->
                                        <div class="mb-3">
                                            <input type="file" class="form-control" id="editImageUpload" multiple accept="image/*" style="display: none;">
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('editImageUpload').click()">
                                                <i class="bi bi-plus-circle"></i> 添加图片
                                            </button>
                                            <small class="text-muted ms-2">支持多张图片上传</small>
                                        </div>
                                        <!-- 图片列表 -->
                                        <div id="editImagePreview" class="row g-2">
                                            <!-- 图片将通过JavaScript动态加载 -->
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 右侧功能区域 -->
                        <div class="col-lg-4">

                            <!-- 话题标签 -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="bi bi-hash"></i> 话题标签
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <input type="text" class="form-control" id="editTopicInput" placeholder="输入话题后按回车添加">
                                    </div>
                                    <div id="editTopicTags" class="d-flex flex-wrap gap-1">
                                        <!-- 话题标签将通过JavaScript动态生成 -->
                                    </div>
                                    <small class="text-muted">按回车或或点击添加话题</small>
                                </div>
                            </div>

                            <!-- @用户 -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="bi bi-at"></i> @用户
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <input type="text" class="form-control" id="editUserInput" placeholder="输入用户名后按回车添加">
                                    </div>
                                    <div id="editUserTags" class="d-flex flex-wrap gap-1">
                                        <!-- @用户标签将通过JavaScript动态生成 -->
                                    </div>
                                    <small class="text-muted">按回车或点击添加@用户</small>
                                </div>
                            </div>

                            <!-- 位置信息 -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="bi bi-geo-alt"></i> 位置信息
                                </div>
                                <div class="card-body">
                                    <input type="text" class="form-control" id="editLocation" placeholder="输入单个位置信息">
                                    <small class="text-muted">输入单个位置信息</small>
                                </div>
                            </div>

                            <!-- 发布设置 -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="bi bi-gear"></i> 发布设置
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label class="form-label">发布优先级</label>
                                        <select class="form-select" id="editPriority">
                                            <option value="normal">普通</option>
                                            <option value="high">高优先级</option>
                                            <option value="urgent">紧急</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 状态信息 -->
                            <div class="card">
                                <div class="card-header">
                                    <i class="bi bi-info-circle"></i> 状态信息
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>当前状态:</strong>
                                        <span class="badge bg-warning" id="editCurrentStatus">待审核</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>客户:</strong>
                                        <span id="editClientName">-</span>
                                    </div>
                                    <div>
                                        <strong>创建时间:</strong>
                                        <span id="editCreateTime">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x"></i> 取消
                    </button>
                    <button type="button" class="btn btn-info" onclick="submitEdit()">
                        <i class="bi bi-check"></i> 保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 驳回理由模态框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle"></i> 驳回文案
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 驳回类型选择 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-list-check"></i> 请选择问题类型：
                        </label>
                        <div class="row g-2">
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="content">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="content" id="client_type_content" checked>
                                            <label class="form-check-label w-100" for="client_type_content">
                                                <i class="bi bi-file-text fs-4 text-primary d-block mb-1"></i>
                                                <strong class="small">文案问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    标题、内容等
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="image">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="image" id="client_type_image">
                                            <label class="form-check-label w-100" for="client_type_image">
                                                <i class="bi bi-image fs-4 text-success d-block mb-1"></i>
                                                <strong class="small">图片问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    质量、内容等
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="both">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="both" id="client_type_both">
                                            <label class="form-check-label w-100" for="client_type_both">
                                                <i class="bi bi-exclamation-circle fs-4 text-danger d-block mb-1"></i>
                                                <strong class="small">两者都有问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    全部重做
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 流转说明 -->
                    <div class="alert alert-info mb-3" id="clientFlowDescription">
                        <i class="bi bi-info-circle"></i>
                        <strong>流转说明：</strong>
                        <span id="clientFlowText">选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容</span>
                    </div>

                    <!-- 快捷理由选择 - 动态更新 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">常用驳回理由：</label>
                        <div class="d-flex flex-wrap gap-2 mb-3" id="clientQuickReasons">
                            <!-- 快捷理由按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 详细理由输入 -->
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label fw-bold">详细说明：</label>
                        <textarea class="form-control" id="rejectReason" rows="4"
                                  placeholder="请详细说明需要修改的地方，以便创作者更好地理解和改进..." required></textarea>
                        <div class="form-text">
                            <i class="bi bi-lightbulb"></i>
                            建议提供具体的修改建议，这样可以提高修改效率
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x"></i> 取消
                    </button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">
                        <i class="bi bi-check"></i> 确认驳回
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片查看模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片查看</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="文案图片" class="img-fluid" style="max-height: 70vh;">
                </div>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 清除可能的缓存问题
        console.clear();
        console.log('=== 响应式客户审核页面加载 ===');
        console.log('当前时间:', new Date().toISOString());
        console.log('页面URL:', window.location.href);
    </script>
    <script>
        // 全局变量
        let currentPage = 1;
        let currentStatus = '';
        let currentContentId = null;
        let isLoading = false;
        let hasMore = true;

        // 从模板获取shareKey，添加更多调试信息
        const templateShareKey = '{{ share_key }}';
        console.log('=== ShareKey 调试信息 ===');
        console.log('原始模板变量:', templateShareKey);
        console.log('变量类型:', typeof templateShareKey);
        console.log('变量长度:', templateShareKey ? templateShareKey.length : 'N/A');
        console.log('是否为空字符串:', templateShareKey === '');
        console.log('是否为undefined字符串:', templateShareKey === 'undefined');

        // 验证shareKey
        if (!templateShareKey || templateShareKey === 'undefined' || templateShareKey.trim() === '') {
            console.error('❌ ShareKey验证失败!');
            console.error('模板变量值:', templateShareKey);
            alert('页面加载错误：分享密钥未找到\n请检查链接是否正确');
            throw new Error('ShareKey验证失败'); // 阻止进一步执行
        }

        const shareKey = templateShareKey;
        console.log('✅ ShareKey验证成功:', shareKey);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadContent();
            initFilterTabs();
        });

        // 获取访问密钥
        function getAccessKey() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('key');
        }

        // 构建API URL
        function buildApiUrl(path) {
            const accessKey = getAccessKey();
            const url = `/client-review/api/${shareKey}${path}`;
            if (accessKey) {
                const separator = path.includes('?') ? '&' : '?';
                return `${url}${separator}key=${accessKey}`;
            }
            return url;
        }

        // 初始化筛选标签
        function initFilterTabs() {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 更新激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 重置并加载新内容
                    currentStatus = this.dataset.status;
                    currentPage = 1;
                    hasMore = true;
                    document.getElementById('contentList').innerHTML = '';
                    loadContent();
                });
            });
        }

        // 加载统计信息
        function loadStats() {
            fetch(buildApiUrl('/stats'))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('totalCount').textContent = stats.total_count;
                        document.getElementById('pendingCount').textContent = stats.pending_count;
                        document.getElementById('reviewedCount').textContent = stats.reviewed_count;
                        document.getElementById('publishedCount').textContent = stats.published_count;
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                });
        }

        // 加载内容列表
        function loadContent() {
            if (isLoading || !hasMore) return;

            isLoading = true;
            const params = new URLSearchParams({
                page: currentPage,
                per_page: 10,
                status: currentStatus
            });

            fetch(buildApiUrl(`/contents?${params}`))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const contents = data.contents;
                        if (contents.length === 0) {
                            if (currentPage === 1) {
                                showEmptyState();
                            } else {
                                hasMore = false;
                                hideLoadMore();
                            }
                        } else {
                            renderContents(contents);
                            currentPage++;

                            // 检查是否还有更多内容
                            if (contents.length < 10) {
                                hasMore = false;
                                hideLoadMore();
                            } else {
                                showLoadMore();
                            }
                        }
                    } else {
                        showError('加载内容失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('加载内容失败:', error);
                    showError('网络错误，请稍后重试');
                })
                .finally(() => {
                    isLoading = false;
                });
        }

        // 渲染内容列表
        function renderContents(contents) {
            const container = document.getElementById('contentList');

            contents.forEach(content => {
                const contentCard = createContentCard(content);
                container.appendChild(contentCard);
            });

            hideEmptyState();
        }

        // 创建内容卡片
        function createContentCard(content) {
            const card = document.createElement('div');
            card.className = 'content-card';

            // 检测屏幕宽度决定点击行为
            const isMobile = window.innerWidth < 768;

            if (isMobile) {
                // 移动端：点击卡片跳转到详情页
                card.addEventListener('click', function(e) {
                    // 如果点击的是按钮，不跳转
                    if (e.target.closest('.action-btn')) {
                        return;
                    }
                    const accessKey = getAccessKey();
                    let detailUrl = `/client-review/${shareKey}/content/${content.id}`;
                    if (accessKey) {
                        detailUrl += `?key=${accessKey}`;
                    }
                    window.location.href = detailUrl;
                });
            }

            // 根据工作流状态和客户审核状态确定显示状态
            function getDisplayStatus(content) {
                // 已发布
                if (content.publish_status === 'published') {
                    return { class: 'status-published', text: '已发布' };
                }

                // 客户审核通过的情况（包括手动通过和自动通过）
                if (content.client_review_status === 'approved' || content.client_review_status === 'auto_approved') {
                    // 根据工作流状态判断具体状态
                    if (content.workflow_status === 'ready_to_publish') {
                        return { class: 'status-approved', text: '待发布' };
                    } else if (content.workflow_status === 'pending_publish') {
                        return { class: 'status-approved', text: '待发布' };
                    } else if (content.workflow_status === 'publishing') {
                        return { class: 'status-approved', text: '发布中' };
                    } else {
                        return { class: 'status-approved', text: '已通过' };
                    }
                }

                // 待审核：客户审核状态为pending
                if (content.client_review_status === 'pending' && content.workflow_status === 'pending_client_review') {
                    return { class: 'status-pending', text: '待审核' };
                }

                // 其他情况
                return { class: 'status-pending', text: '待审核' };
            }

            const status = getDisplayStatus(content);

            // 构建图片网格
            let imagesHtml = '';
            if (content.images && content.images.length > 0) {
                imagesHtml = '<div class="content-images">';
                content.images.slice(0, 6).forEach(image => {
                    // ContentImage.to_dict() 返回的字段名是 image_path
                    let imageUrl = image.image_path || image.url || image;

                    // 如果是相对路径，转换为正确的静态文件路径
                    if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
                        // 图片实际存储在 /static/uploads/images/ 目录中
                        imageUrl = `/static/uploads/${imageUrl}`;
                    }

                    console.log('图片数据:', image, '使用URL:', imageUrl);
                    imagesHtml += `
                        <div class="content-image" onclick="showImageModal('${imageUrl}')">
                            <img src="${imageUrl}" alt="文案图片" loading="lazy" onerror="console.error('图片加载失败:', '${imageUrl}')">
                        </div>
                    `;
                });
                imagesHtml += '</div>';
            }

            // 构建操作按钮
            let actionsHtml = '';

            // 调试信息：显示文案状态
            console.log(`文案 ${content.id} 状态: workflow_status=${content.workflow_status}, client_review_status=${content.client_review_status}`);

            if (isMobile) {
                // 移动端按钮
                if (content.workflow_status === 'pending_client_review' && content.client_review_status === 'pending') {
                    actionsHtml = `
                        <div class="content-actions">
                            <button class="action-btn btn-approve" onclick="approveContent(${content.id})">
                                <i class="bi bi-check-lg me-1"></i>通过
                            </button>
                            <button class="action-btn btn-reject" onclick="showRejectModal(${content.id})">
                                <i class="bi bi-x-lg me-1"></i>驳回
                            </button>
                            <button class="action-btn btn-edit" onclick="showEditModal(${content.id})">
                                <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                        </div>
                    `;
                } else {
                    // 即使不是待审核状态，也显示编辑按钮用于测试
                    actionsHtml = `
                        <div class="content-actions">
                            <button class="action-btn btn-edit" onclick="showEditModal(${content.id})">
                                <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                        </div>
                    `;
                }
            } else {
                // 桌面端按钮
                actionsHtml = `
                    <div class="content-actions">
                        <button class="action-btn btn-view" onclick="viewContent(${content.id})">
                            <i class="bi bi-eye me-1"></i>查看
                        </button>
                        ${content.workflow_status === 'pending_client_review' && content.client_review_status === 'pending' ? `
                            <button class="action-btn btn-approve" onclick="approveContent(${content.id})">
                                <i class="bi bi-check me-1"></i>通过
                            </button>
                            <button class="action-btn btn-reject" onclick="showRejectModal(${content.id})">
                                <i class="bi bi-x me-1"></i>驳回
                            </button>
                            <button class="action-btn btn-edit" onclick="showEditModal(${content.id})">
                                <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                        ` : `
                            <button class="action-btn btn-edit" onclick="showEditModal(${content.id})">
                                <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                        `}
                    </div>
                `;
            }

            card.innerHTML = `
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="content-title">${content.title || '无标题'}</h3>
                        <div class="d-flex align-items-center gap-2">
                            <span class="content-status ${status.class}">${status.text}</span>
                        </div>
                    </div>
                </div>
                <div class="content-body">
                    <div class="content-text">${content.content || ''}</div>
                    ${imagesHtml}
                    <div class="content-meta">
                        <span>创建时间: ${formatDate(content.created_at)}</span>
                        <span>任务: ${content.task_name || '默认任务'}</span>
                    </div>
                </div>
                ${actionsHtml}
            `;

            return card;
        }

        // 查看文案详情（桌面端）
        function viewContent(contentId) {
            const accessKey = getAccessKey();
            let detailUrl = `/client-review/${shareKey}/content/${contentId}`;
            if (accessKey) {
                detailUrl += `?key=${accessKey}`;
            }
            window.location.href = detailUrl;
        }

        // 显示编辑模态框
        function showEditModal(contentId) {
            console.log('显示编辑模态框，contentId:', contentId);
            currentContentId = contentId;

            // 获取文案详情
            const apiUrl = buildApiUrl(`/contents/${contentId}`);
            console.log('请求API URL:', apiUrl);

            fetch(apiUrl)
                .then(response => {
                    console.log('API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('API响应数据:', data);
                    if (data.success) {
                        const content = data.content;

                        // 填充基本信息
                        document.getElementById('editTitle').value = content.title || '';
                        document.getElementById('editContent').value = content.content || '';
                        document.getElementById('editLocation').value = content.location || '';

                        // 更新字符计数
                        updateCharCount('editTitle', 'titleCount');
                        updateCharCount('editContent', 'contentCount');

                        // 处理话题标签
                        initEditTopicTags(content.topics_text || '');

                        // 处理@用户标签
                        initEditUserTags(content.mentions || '');

                        // 加载图片预览
                        loadEditImagePreview(contentId);

                        // 更新状态信息
                        updateEditStatusInfo(content);

                        // 显示模态框
                        const modal = new bootstrap.Modal(document.getElementById('editModal'));
                        modal.show();
                    } else {
                        showToast('获取文案详情失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('获取文案详情失败:', error);
                    showToast('网络错误，请稍后重试', 'error');
                });
        }

        // 更新字符计数
        function updateCharCount(inputId, countId) {
            const input = document.getElementById(inputId);
            const count = document.getElementById(countId);
            const length = input.value.length;
            count.textContent = length;

            // 更新警告状态
            if (inputId === 'editTitle') {
                const warning = document.getElementById('titleWarning');
                if (length > 20) {
                    warning.style.display = 'inline';
                } else {
                    warning.style.display = 'none';
                }
            } else if (inputId === 'editContent') {
                const requirement = document.getElementById('contentRequirement');
                if (length > 1000) {
                    requirement.textContent = '超出限制!';
                    requirement.className = 'text-danger';
                } else {
                    requirement.textContent = '符合要求';
                    requirement.className = 'text-success';
                }
            }
        }

        // 初始化话题标签
        function initEditTopicTags(topicsText) {
            const container = document.getElementById('editTopicTags');
            container.innerHTML = '';

            if (topicsText) {
                const topics = topicsText.split(',').map(t => t.trim()).filter(t => t);
                topics.forEach(topic => {
                    addEditTopicTag(topic);
                });
            }
        }

        // 添加话题标签
        function addEditTopicTag(topic) {
            if (!topic) return;

            const container = document.getElementById('editTopicTags');
            const tag = document.createElement('span');
            tag.className = 'badge bg-primary me-1 mb-1';
            tag.innerHTML = `
                #${topic}
                <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" onclick="removeEditTag(this)"></button>
            `;
            container.appendChild(tag);
        }

        // 初始化用户标签
        function initEditUserTags(mentionsText) {
            const container = document.getElementById('editUserTags');
            container.innerHTML = '';

            if (mentionsText) {
                const mentions = mentionsText.split(',').map(m => m.trim()).filter(m => m);
                mentions.forEach(mention => {
                    addEditUserTag(mention);
                });
            }
        }

        // 添加用户标签
        function addEditUserTag(user) {
            if (!user) return;

            const container = document.getElementById('editUserTags');
            const tag = document.createElement('span');
            tag.className = 'badge bg-info me-1 mb-1';
            tag.innerHTML = `
                @${user}
                <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" onclick="removeEditTag(this)"></button>
            `;
            container.appendChild(tag);
        }

        // 移除标签
        function removeEditTag(button) {
            button.parentElement.remove();
        }

        // 加载图片预览 - 使用客户审核专用API
        function loadEditImagePreview(contentId) {
            console.log('加载图片预览，contentId:', contentId);
            const container = document.getElementById('editImagePreview');
            container.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> 加载中...</div>';

            // 使用客户审核专用的API
            const imageApiUrl = buildApiUrl(`/images/${contentId}`);
            console.log('图片API URL:', imageApiUrl);

            fetch(imageApiUrl)
                .then(response => {
                    console.log('图片API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('图片API响应数据:', data);
                    if (data.success && data.images && data.images.length > 0) {
                        console.log(`找到 ${data.images.length} 张图片，开始渲染`);
                        container.innerHTML = '';
                        data.images.forEach((image, index) => {
                            const imgDiv = document.createElement('div');
                            imgDiv.className = 'col-6 col-md-4 col-lg-3 image-item';
                            imgDiv.setAttribute('data-image-id', image.id);
                            imgDiv.setAttribute('draggable', 'true');
                            imgDiv.innerHTML = `
                                <div class="card position-relative">
                                    <div class="position-absolute top-0 start-0 m-1">
                                        <span class="badge bg-primary">${index + 1}</span>
                                    </div>
                                    <img src="/static/uploads/${image.image_path}" class="card-img-top" style="height: 120px; object-fit: cover;" alt="配图">
                                    <div class="card-body p-2">
                                        <small class="text-muted text-truncate" title="${image.original_name}">
                                            ${image.original_name && image.original_name.length > 15 ? image.original_name.substring(0, 15) + '...' : (image.original_name || '图片')}
                                        </small>
                                    </div>
                                    <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                                            onclick="deleteEditImage(${image.id})" title="删除图片">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            `;
                            container.appendChild(imgDiv);
                        });

                        console.log('图片渲染完成，初始化拖拽排序功能');
                        // 初始化拖拽排序功能
                        initEditImageSorting();
                    } else {
                        console.log('没有找到图片，显示空状态');
                        container.innerHTML = '<div class="text-muted text-center py-3">暂无配图，点击上方按钮添加图片</div>';
                    }
                })
                .catch(error => {
                    console.error('加载图片失败:', error);
                    container.innerHTML = '<div class="text-muted text-center py-3">加载失败</div>';
                });
        }

        // 删除图片 - 使用客户审核专用API
        function deleteEditImage(imageId) {
            if (!confirm('确定要删除这张图片吗？')) {
                return;
            }

            const deleteApiUrl = buildApiUrl(`/images/${imageId}`);
            console.log('删除图片API URL:', deleteApiUrl);

            fetch(deleteApiUrl, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('图片删除成功', 'success');
                    loadEditImagePreview(currentContentId); // 重新加载图片列表
                } else {
                    showToast(data.message || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('删除图片失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
        }

        // 上传图片 - 使用客户审核专用API
        function uploadEditImages(files) {
            if (!files || files.length === 0) return;

            // 显示上传进度
            const container = document.getElementById('editImagePreview');
            const uploadingId = 'uploading-' + Date.now(); // 使用唯一ID
            const uploadingDiv = document.createElement('div');
            uploadingDiv.className = 'col-12';
            uploadingDiv.id = uploadingId;
            uploadingDiv.innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    正在上传图片...
                </div>
            `;
            container.appendChild(uploadingDiv);

            // 逐个上传文件，使用客户审核专用API
            let uploadCount = 0;
            let successCount = 0;

            Array.from(files).forEach((file, index) => {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('content_id', currentContentId);

                const uploadApiUrl = buildApiUrl('/upload/image');
                console.log('上传图片API URL:', uploadApiUrl);

                fetch(uploadApiUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('上传响应状态:', response.status);
                    console.log('上传响应头:', response.headers);

                    if (!response.ok) {
                        return response.text().then(errorText => {
                            console.error('上传错误响应文本:', errorText);
                            try {
                                const errorData = JSON.parse(errorText);
                                throw new Error(errorData.message || `HTTP ${response.status}`);
                            } catch (parseError) {
                                throw new Error(`HTTP ${response.status}: ${errorText}`);
                            }
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('上传响应数据:', data);
                    uploadCount++;
                    if (data.success) {
                        successCount++;
                        console.log('图片上传成功:', data);
                    } else {
                        console.error('图片上传失败:', data.message);
                    }

                    // 所有文件上传完成
                    if (uploadCount === files.length) {
                        console.log('所有文件上传完成，移除上传提示');
                        // 使用ID来确保移除上传提示
                        const uploadingElement = document.getElementById(uploadingId);
                        if (uploadingElement) {
                            uploadingElement.remove();
                            console.log('上传提示已移除');
                        } else {
                            console.log('未找到上传提示元素');
                        }

                        if (successCount > 0) {
                            showToast(`成功上传 ${successCount} 张图片`, 'success');
                            console.log('开始重新加载图片列表');
                            // 添加小延迟确保数据库事务提交
                            setTimeout(() => {
                                loadEditImagePreview(currentContentId);
                            }, 500);
                        } else {
                            showToast('图片上传失败', 'error');
                        }
                    }
                })
                .catch(error => {
                    uploadCount++;
                    console.error('上传图片失败详细信息:', error);
                    console.error('错误堆栈:', error.stack);

                    // 所有文件上传完成
                    if (uploadCount === files.length) {
                        console.log('所有文件处理完成（包含错误），移除上传提示');
                        // 使用ID来确保移除上传提示
                        const uploadingElement = document.getElementById(uploadingId);
                        if (uploadingElement) {
                            uploadingElement.remove();
                            console.log('上传提示已移除（错误处理）');
                        } else {
                            console.log('未找到上传提示元素（错误处理）');
                        }

                        if (successCount > 0) {
                            showToast(`成功上传 ${successCount} 张图片，${files.length - successCount} 张失败`, 'warning');
                            console.log('开始重新加载图片列表（部分成功）');
                            // 添加小延迟确保数据库事务提交
                            setTimeout(() => {
                                loadEditImagePreview(currentContentId);
                            }, 500);
                        } else {
                            showToast('图片上传失败: ' + error.message, 'error');
                        }
                    }
                });
            });
        }

        // 更新状态信息
        function updateEditStatusInfo(content) {
            document.getElementById('editCurrentStatus').textContent = getStatusText(content.workflow_status);
            document.getElementById('editClientName').textContent = content.client_name || '-';
            document.getElementById('editCreateTime').textContent = content.created_at ? formatDate(content.created_at) : '-';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending_client_review': '待审核',
                'approved': '已通过',
                'rejected': '已驳回',
                'published': '已发布'
            };
            return statusMap[status] || status;
        }

        // 初始化图片拖拽排序功能
        let draggedElement = null;
        let isImageSorting = false;

        function initEditImageSorting() {
            console.log('初始化图片拖拽排序功能');

            const imageItems = document.querySelectorAll('.image-item');

            imageItems.forEach(item => {
                // 拖拽开始
                item.addEventListener('dragstart', function(e) {
                    console.log('拖拽开始:', this.dataset.imageId);
                    draggedElement = this;
                    isImageSorting = true;
                    this.style.opacity = '0.5';

                    e.dataTransfer.effectAllowed = 'move';
                    e.dataTransfer.setData('text/html', this.outerHTML);
                    e.dataTransfer.setData('text/plain', this.dataset.imageId);
                });

                // 拖拽结束
                item.addEventListener('dragend', function(e) {
                    console.log('拖拽结束');
                    this.style.opacity = '1';
                    draggedElement = null;

                    setTimeout(() => {
                        isImageSorting = false;
                    }, 100);
                });

                // 拖拽进入
                item.addEventListener('dragenter', function(e) {
                    if (isImageSorting && draggedElement !== this) {
                        e.preventDefault();
                        this.style.transform = 'scale(1.05)';
                    }
                });

                // 拖拽离开
                item.addEventListener('dragleave', function(e) {
                    if (isImageSorting) {
                        this.style.transform = 'scale(1)';
                    }
                });

                // 拖拽悬停
                item.addEventListener('dragover', function(e) {
                    if (isImageSorting && draggedElement !== this) {
                        e.preventDefault();
                        e.dataTransfer.dropEffect = 'move';
                    }
                });

                // 放置
                item.addEventListener('drop', function(e) {
                    if (isImageSorting && draggedElement !== this) {
                        e.preventDefault();
                        this.style.transform = 'scale(1)';

                        // 交换位置
                        const container = this.parentNode;
                        const draggedIndex = Array.from(container.children).indexOf(draggedElement);
                        const targetIndex = Array.from(container.children).indexOf(this);

                        if (draggedIndex < targetIndex) {
                            container.insertBefore(draggedElement, this.nextSibling);
                        } else {
                            container.insertBefore(draggedElement, this);
                        }

                        // 更新序号
                        updateImageNumbers();

                        // 保存排序
                        saveEditImageOrder();
                    }
                });
            });
        }

        // 更新图片序号
        function updateImageNumbers() {
            const imageItems = document.querySelectorAll('.image-item');
            imageItems.forEach((item, index) => {
                const badge = item.querySelector('.badge');
                if (badge) {
                    badge.textContent = index + 1;
                }
            });
        }

        // 保存图片排序 - 使用客户审核专用API
        function saveEditImageOrder() {
            if (!currentContentId) {
                console.error('没有当前文案ID，无法保存排序');
                return;
            }

            console.log('保存图片排序');

            const imageItems = document.querySelectorAll('.image-item');
            const orderData = [];

            imageItems.forEach((item, index) => {
                orderData.push({
                    id: parseInt(item.dataset.imageId),
                    order: index + 1
                });
            });

            console.log('排序数据:', orderData);

            // 使用客户审核专用的API
            const reorderApiUrl = buildApiUrl('/images/reorder');
            console.log('排序API URL:', reorderApiUrl);

            fetch(reorderApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image_orders: orderData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('图片排序保存成功');
                    showToast('图片排序已保存', 'success');
                } else {
                    console.error('图片排序保存失败:', data.message);
                    showToast('排序保存失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('图片排序请求失败:', error);
                showToast('排序保存失败', 'error');
            });
        }

        // 提交编辑 - 使用现有系统的API
        function submitEdit() {
            const title = document.getElementById('editTitle').value.trim();
            const content = document.getElementById('editContent').value.trim();
            const location = document.getElementById('editLocation').value.trim();

            if (!title) {
                showToast('请输入标题', 'error');
                return;
            }

            if (!content) {
                showToast('请输入内容', 'error');
                return;
            }

            if (title.length > 20) {
                showToast('标题不能超过20个字符', 'error');
                return;
            }

            if (content.length > 1000) {
                showToast('内容不能超过1000个字符', 'error');
                return;
            }

            // 收集话题标签
            const topicTags = Array.from(document.querySelectorAll('#editTopicTags .badge'))
                .map(tag => tag.textContent.replace('#', '').replace('×', '').trim())
                .filter(tag => tag);

            // 收集用户标签
            const userTags = Array.from(document.querySelectorAll('#editUserTags .badge'))
                .map(tag => tag.textContent.replace('@', '').replace('×', '').trim())
                .filter(tag => tag);

            // 使用现有系统的API格式
            const formData = new FormData();
            formData.append('title', title);
            formData.append('content', content);
            formData.append('topics', topicTags.join(','));
            formData.append('location', location);

            // 处理@用户标签 - 转换为JSON格式
            if (userTags.length > 0) {
                formData.append('at_users', JSON.stringify(userTags));
            }

            // 使用客户审核的编辑API
            fetch(buildApiUrl(`/contents/${currentContentId}/edit`), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('文案修改成功', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    loadContent(); // 重新加载内容列表
                } else {
                    showToast(data.message || '修改失败', 'error');
                }
            })
            .catch(error => {
                console.error('修改失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
        }

        // 显示驳回模态框
        function showRejectModal(contentId) {
            currentContentId = contentId;
            document.getElementById('rejectReason').value = '';

            // 重置驳回类型选择为默认值（文案问题）
            document.getElementById('client_type_content').checked = true;

            // 重置卡片选中状态
            const typeCards = document.querySelectorAll('#rejectModal .rejection-type-card');
            typeCards.forEach(card => card.classList.remove('selected'));
            document.querySelector('#rejectModal .rejection-type-card[data-type="content"]').classList.add('selected');

            // 初始化驳回类型选择事件
            initClientRejectionTypeSelection();

            // 更新快捷理由和流转说明
            updateClientQuickReasonsAndFlow('content');

            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // 填充快捷驳回理由
        function fillRejectReason(reason) {
            const textarea = document.getElementById('rejectReason');
            const currentValue = textarea.value.trim();
            if (currentValue) {
                textarea.value = currentValue + '\n' + reason;
            } else {
                textarea.value = reason;
            }
        }

        // 初始化客户审核的驳回类型选择
        function initClientRejectionTypeSelection() {
            // 为客户审核的驳回类型卡片添加点击事件
            const typeCards = document.querySelectorAll('#rejectModal .rejection-type-card');
            typeCards.forEach(card => {
                card.addEventListener('click', function() {
                    const type = this.dataset.type;
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio) {
                        radio.checked = true;
                        // 更新卡片样式
                        typeCards.forEach(c => c.classList.remove('selected'));
                        this.classList.add('selected');
                        // 更新快捷理由和流转说明
                        updateClientQuickReasonsAndFlow(type);
                    }
                });
            });
        }

        // 更新客户审核的快捷理由和流转说明
        function updateClientQuickReasonsAndFlow(type) {
            const quickReasonsContainer = document.getElementById('clientQuickReasons');
            const flowText = document.getElementById('clientFlowText');

            // 清空现有的快捷理由
            quickReasonsContainer.innerHTML = '';

            // 根据驳回类型生成快捷理由
            let reasons = [];
            let flowDescription = '';

            switch(type) {
                case 'content':
                    reasons = [
                        '标题不够吸引人，建议重新设计',
                        '内容逻辑不清晰，需要重新组织',
                        '话题标签不合适，需要调整',
                        '文案长度不合适，需要调整',
                        '内容与品牌调性不符，需要调整风格',
                        '缺少关键信息，需要补充完整'
                    ];
                    flowDescription = '选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容';
                    break;
                case 'image':
                    reasons = [
                        '图片质量不符合要求，需要更换',
                        '图片内容与文案不匹配',
                        '图片数量不足，需要补充',
                        '图片尺寸或格式不合适',
                        '图片清晰度不够，影响展示效果',
                        '图片风格与品牌不符'
                    ];
                    flowDescription = '选择"图片问题"：文案将回到<strong>图片管理</strong>，需要重新上传或调整图片';
                    break;
                case 'both':
                    reasons = [
                        '文案和图片都需要重新设计',
                        '整体质量不符合标准，需要全面优化',
                        '内容和视觉效果都需要提升',
                        '文案与图片配合度不够，需要重新匹配',
                        '整体创意需要重新构思',
                        '品牌一致性问题，需要全面调整'
                    ];
                    flowDescription = '选择"两者都有问题"：文案将回到<strong>草稿状态</strong>，需要重新编写文案并上传图片';
                    break;
            }

            // 更新流转说明
            flowText.innerHTML = flowDescription;

            // 生成快捷理由按钮
            reasons.forEach(reason => {
                const btn = document.createElement('button');
                btn.type = 'button';
                btn.className = 'btn btn-outline-secondary btn-sm quick-reason-btn';
                btn.textContent = reason;
                btn.onclick = function() {
                    fillRejectReason(reason);
                    // 切换按钮选中状态
                    this.classList.toggle('selected');
                };
                quickReasonsContainer.appendChild(btn);
            });
        }

        // 通过审核
        function approveContent(contentId) {
            if (!confirm('确定要通过这篇文案吗？')) return;

            reviewContent(contentId, 'approve', '');
        }

        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            if (!reason) {
                alert('请输入驳回理由');
                return;
            }

            // 获取驳回类型
            const rejectionType = document.querySelector('input[name="rejection_type"]:checked').value;

            reviewContent(currentContentId, 'reject', reason, rejectionType);
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
        }

        // 审核内容
        function reviewContent(contentId, action, comment, rejectionType = null) {
            // 禁用所有审核按钮防止重复提交
            const approveBtns = document.querySelectorAll('.btn-approve');
            const rejectBtns = document.querySelectorAll('.btn-reject');
            
            approveBtns.forEach(btn => btn.disabled = true);
            rejectBtns.forEach(btn => btn.disabled = true);

            const formData = new FormData();
            formData.append('action', action);
            formData.append('review_comment', comment);
            
            // 如果是驳回操作，添加驳回类型
            if (action === 'reject' && rejectionType) {
                formData.append('rejection_type', rejectionType);
            }

            fetch(buildApiUrl(`/contents/${contentId}/review`), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    // 重新加载当前页面内容
                    refreshCurrentContent();
                    // 更新统计信息
                    loadStats();
                } else {
                    showToast(data.message, 'error');
                    // 重新启用所有按钮
                    approveBtns.forEach(btn => btn.disabled = false);
                    rejectBtns.forEach(btn => btn.disabled = false);
                }
            })
            .catch(error => {
                console.error('审核失败:', error);
                showToast('网络错误，请稍后重试', 'error');
                // 重新启用所有按钮
                approveBtns.forEach(btn => btn.disabled = false);
                rejectBtns.forEach(btn => btn.disabled = false);
            });
        }

        // 刷新当前内容
        function refreshCurrentContent() {
            currentPage = 1;
            hasMore = true;
            document.getElementById('contentList').innerHTML = '';
            loadContent();
        }

        // 加载更多内容
        function loadMoreContent() {
            loadContent();
        }

        // 显示/隐藏加载更多按钮
        function showLoadMore() {
            document.getElementById('loadMore').style.display = 'block';
        }

        function hideLoadMore() {
            document.getElementById('loadMore').style.display = 'none';
        }

        // 显示/隐藏空状态
        function showEmptyState() {
            document.getElementById('emptyState').style.display = 'block';
            hideLoadMore();
        }

        function hideEmptyState() {
            document.getElementById('emptyState').style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            showToast(message, 'error');
        }

        // 显示图片模态框
        function showImageModal(imageUrl) {
            document.getElementById('modalImage').src = imageUrl;
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
        }

        // 显示提示信息
        function showToast(message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-primary';
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 监听窗口大小变化，重新渲染内容
        window.addEventListener('resize', function() {
            // 防抖处理
            clearTimeout(window.resizeTimeout);
            window.resizeTimeout = setTimeout(function() {
                refreshCurrentContent();
            }, 300);
        });

        // 页面加载完成后初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 为编辑表单添加字符计数事件
            const titleInput = document.getElementById('editTitle');
            const contentInput = document.getElementById('editContent');
            const topicInput = document.getElementById('editTopicInput');
            const userInput = document.getElementById('editUserInput');

            if (titleInput) {
                titleInput.addEventListener('input', function() {
                    updateCharCount('editTitle', 'titleCount');
                });
            }

            if (contentInput) {
                contentInput.addEventListener('input', function() {
                    updateCharCount('editContent', 'contentCount');
                });
            }

            // 话题标签输入事件
            if (topicInput) {
                topicInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const topic = this.value.trim();
                        if (topic) {
                            addEditTopicTag(topic);
                            this.value = '';
                        }
                    }
                });
            }

            // 用户标签输入事件
            if (userInput) {
                userInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const user = this.value.trim();
                        if (user) {
                            addEditUserTag(user);
                            this.value = '';
                        }
                    }
                });
            }

            // 图片上传事件
            const imageUpload = document.getElementById('editImageUpload');
            if (imageUpload) {
                imageUpload.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files.length > 0) {
                        uploadEditImages(e.target.files);
                        // 清空文件输入，允许重复上传同一文件
                        e.target.value = '';
                    }
                });
            }
        });
    </script>
</body>
</html>
